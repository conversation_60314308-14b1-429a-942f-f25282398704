import { render, h, reactive } from 'vue'
import { useRender } from '/@/components/Table/src/hooks/useRender'
import { Button, Popconfirm, Select, message } from 'ant-design-vue'
import { receiptsetPaymentType, receiptsetUrgentLevel, setDeptId, setMxPaymentType } from '/@/api/financialDocuments/receiptOrder'
import { financepcsetContractingParty, setPaymentType, setUrgentLevel } from '/@/api/financialDocuments/paymentOrder'
import { useSaleOrderStore } from '/@/store/modules/saleOrder'
import PagingApiSelect from '/@/components/Form/src/components/PagingApiSelect.vue'
import { getDept } from '/@/api/erp/systemInfo'
import { expensetIsDisabled } from '/@/api/baseData/expenseitem'
import { cpgetList } from '/@/api/erp/purchaseOrder'

const saleOrderStore = useSaleOrderStore()

/** 是否作废 */
export function getInvalidStatus(invalidStatus: number) {
  const mapValue = {
    0: { text: '否', color: 'green' },
    1: { text: '是', color: 'red' }
  }
  return useRender.renderTag(mapValue[invalidStatus]?.text, mapValue[invalidStatus]?.color)
}

/** 是否作废自定义指令 */
export const vInvalid = {
  beforeUpdate(el, binding) {
    const vnode = getInvalidStatus(binding.value)
    render(vnode, el)
  }
}

/** 判断款项类型 */
export function paymentTypeFn(type = 4) {
  const mapValue = {
    1: { text: '定金', color: '' },
    2: { text: '最后一笔款', color: '' },
    3: { text: '全款', color: '' },
    4: { text: '-', color: '' }
  }
  return useRender.renderTag(mapValue[type].text, mapValue[type].color)
}

/** 判款项类型自定义指令 */
export const vPtype = {
  beforeUpdate(el, binding) {
    const vnode = paymentTypeFn(binding.value)
    render(vnode, el)
  }
}

/** 判断款单类型 */
export function determineType(type) {
  const mapValue = {
    4: { text: '采购单', color: '' },
    8: { text: '其他支出单', color: '' },
    11: { text: '退款单', color: '' }
  }
  return useRender.renderTag(mapValue[type].text, mapValue[type].color)
}

/** 判断款单类型自定义指令 */
export const vClause = {
  beforeUpdate(el, binding) {
    const vnode = useRender.renderTag(saleOrderStore.orderType[binding.value])
    render(vnode, el)
  }
}

/** 紧急状态 */
export function stateOfEmergency(invalidStatus: number) {
  const mapValue = {
    1: { text: '一般', color: '' },
    2: { text: '紧急', color: 'red' },
    3: { text: '-', color: '' }
  }
  return useRender.renderTag(mapValue[invalidStatus]?.text, mapValue[invalidStatus]?.color)
}

/** 紧急状态自定义指令 */
export const vEmergency = {
  beforeUpdate(el, binding) {
    const vnode = stateOfEmergency(binding.value)
    render(vnode, el)
  }
}

/** 给详情页添加当前需 收/付 金额*/
export const addCurrentAmount = (dataSource: Array<any> = []) => {
  const newDataSource = dataSource.map((item) => {
    item['current_amount'] = item.amount
    return item
  })
  return newDataSource
}

/******************** 款项类型 *******************/
const data = reactive({
  select: ''
})
// 监听点击已付款时输入备注的值
const handleSelect = (e) => {
  data.select = e
}
// Popconfirm组件点击显示隐藏触发事件
function handleVisibleChange(defaultValue) {
  data.select = defaultValue
}

export const mapValue = {
  1: { text: '定金', color: '' },
  2: { text: '最后一笔款', color: '' },
  3: { text: '全款', color: '' }
}
export const paymenttype = Object.keys(mapValue).map((item) => ({
  value: Number(item),
  label: mapValue[item].text
}))

/**
 *
 * @param payment_type 字段名
 * @param isChange 是否可以点击编辑，true：可以编辑，false：不可以编辑
 * @param fetchType 接口类型，2：付款单，1:收款单
 * @param reload 表格刷新方法
 * @returns
 */
export const paymentType = (id, payment_type, isChange = false, fetchType: 1 | 2, reload) => {
  return h(
    Popconfirm,
    {
      placement: 'right',
      disabled: !isChange,
      title: h(Select, {
        options: [
          { label: '定金', value: 1 },
          { label: '最后一笔款', value: 2 },
          { label: '全款', value: 3 }
        ],
        'onUpdate:value': handleSelect,
        style: { width: '100px' },
        value: data.select,
        getPopupContainer: (trigger) => trigger.parentNode
      }),
      onConfirm: async () => {
        try {
          fetchType == 1
            ? await receiptsetPaymentType({ id: id, payment_type: data.select })
            : await setPaymentType({ id: id, payment_type: data.select })
          message.success('修改成功')
          reload()
        } catch (error) {
          console.log(error)
        }
      },
      onVisibleChange: (val) => {
        if (val) {
          handleVisibleChange(payment_type)
        }
      }
    },
    { default: () => h(Button, { size: 'small' }, () => mapValue[payment_type]?.text) }
    //
  )
}

/**
 *
 * @param urgent_level 字段名
 * @param isChange 是否可以点击编辑，true：可以编辑，false：不可以编辑
 * @param fetchType 接口类型，2：付款单，1:收款单
 * @param reload 表格刷新方法
 * @returns
 */
export const urgentLevel = (id, urgent_level, isChange = false, fetchType: 1 | 2, reload) => {
  const urgentVlue = {
    1: { text: '一般', color: '' },
    2: { text: '紧急', color: 'red' }
  }
  return h(
    Popconfirm,
    {
      placement: 'right',
      disabled: !isChange,
      title: h(Select, {
        options: [
          { label: '一般', value: 1 },
          { label: '紧急', value: 2 }
        ],
        'onUpdate:value': handleSelect,
        style: { width: '100px' },
        value: data.select,
        getPopupContainer: (trigger) => trigger.parentNode
      }),
      onConfirm: async () => {
        try {
          fetchType == 1
            ? await receiptsetUrgentLevel({ id: id, urgent_level: data.select })
            : await setUrgentLevel({ id: id, urgent_level: data.select })

          message.success('修改成功')
          reload()
        } catch (error) {
          console.log(error)
        }
      },
      onVisibleChange: (val) => {
        if (val) {
          handleVisibleChange(urgent_level)
        }
      }
    },
    { default: () => h(Button, { size: 'small', style: `color:${urgentVlue[urgent_level]?.color}` }, () => urgentVlue[urgent_level]?.text) }
    //
  )
}
/**
 *
 * @param urgent_level 字段名
 * @param isChange 是否可以点击编辑，true：可以编辑，false：不可以编辑
 * @param fetchType 接口类型，2：付款单，1:收款单
 * @param reload 表格刷新方法
 * @returns
 */
export const MxPaymentType = (id, payment_type, isChange = false, fetchType: 1 | 2, reload, record) => {
  return h(
    Popconfirm,
    {
      placement: 'right',
      disabled: !isChange,
      title: h(Select, {
        options: [
          { label: '定金', value: 1 },
          { label: '最后一笔款', value: 2 },
          { label: '全款', value: 3 }
        ],
        'onUpdate:value': handleSelect,
        style: { width: '100px' },
        value: data.select,
        getPopupContainer: (trigger) => trigger.parentNode
      }),
      onConfirm: async () => {
        try {
          await setMxPaymentType({ rfw_id: id, payment_type: data.select })
          message.success('修改成功')
          reload(record.work_id, { ...record, payment_type: data.select })
        } catch (error) {
          console.log(error)
        }
      },
      onVisibleChange: (val) => {
        if (val) {
          handleVisibleChange(payment_type)
        }
      }
    },
    { default: () => h(Button, { size: 'small' }, () => mapValue[payment_type]?.text) }
    //
  )
}

export const deptPaymentType = (id, payment_type, isChange = false, fetchType: 1 | 2, reload) => {
  return h(
    Popconfirm,
    {
      placement: 'right',
      disabled: !isChange,
      title: h(PagingApiSelect, {
        api: getDept,
        params: { status: 1, is_show: 1, is_audit: 1 },
        resultField: 'items',
        valueField: 'id',
        searchMode: true,
        pagingMode: true,
        selectProps: {
          fieldNames: {
            key: 'key',
            value: 'id',
            label: 'name'
          },
          showSearch: true,
          placeholder: '请选择',
          allowClear: true,
          style: {
            width: '100%'
          }
        },
        'onUpdate:value': handleSelect,
        style: { width: '100px' },
        value: data.select,
        getPopupContainer: (trigger) => trigger.parentNode
      }),
      onConfirm: async () => {
        try {
          console.log(data.select)
          await setDeptId({ id: id, dept_id: data.select })
          message.success('修改成功')
          reload()
        } catch (error) {
          console.log(error)
        }
      },
      onVisibleChange: (val) => {
        if (val) {
          handleVisibleChange(payment_type)
        }
      }
    },
    { default: () => h(Button, { size: 'small' }, () => payment_type) }
    //
  )
}

const isdisabled = {
  0: { label: '启用', color: 'success' },
  1: { label: '禁用', color: 'error' }
}

/**
 *
 * @param payment_type 字段名
 * @param isChange 是否可以点击编辑，true：可以编辑，false：不可以编辑
 * @param reload 表格刷新方法
 * @returns
 */

//档案费用项目使用
export const expenmentType = (id, payment_type, isChange = false, reload) => {
  return h(
    Popconfirm,
    {
      placement: 'right',
      disabled: !isChange,
      title: h(Select, {
        options: [
          { label: '启用', value: 0 },
          { label: '禁用', value: 1 }
        ],
        'onUpdate:value': handleSelect,
        style: { width: '100px' },
        value: data.select,
        getPopupContainer: (trigger) => trigger.parentNode
      }),
      onConfirm: async () => {
        try {
          await expensetIsDisabled({ id: id, is_disabled: data.select })
          reload()
        } catch (error) {
          console.log(error)
        }
      },
      onVisibleChange: (val) => {
        if (val) {
          handleVisibleChange(payment_type)
        }
      }
    },
    { default: () => h(Button, { size: 'small', type: isdisabled[payment_type]?.color }, () => isdisabled[payment_type]?.label) }
    //
  )
}

/**
 *
 * @param payment_type 字段名
 * @param isChange 是否可以点击编辑，true：可以编辑，false：不可以编辑
 * @param reload 表格刷新方法
 * @returns
 */

//付款单签约主体使用
export const contractingParty = (id, payment_type, isChange = false, reload) => {
  return h(
    Popconfirm,
    {
      placement: 'right',
      disabled: !isChange,
      title: h(PagingApiSelect, {
        api: cpgetList,
        params: { status: 1, is_show: 1, is_audit: 1 },
        resultField: 'items',
        valueField: 'id',
        searchMode: true,
        pagingMode: true,
        selectProps: {
          fieldNames: {
            key: 'key',
            value: 'name',
            label: 'name'
          },
          showSearch: true,
          placeholder: '请选择',
          allowClear: true,
          style: {
            width: '100%'
          }
        },
        'onUpdate:value': handleSelect,
        style: { width: '100px' },
        value: data.select,
        getPopupContainer: (trigger) => trigger.parentNode
      }),
      onConfirm: async () => {
        try {
          console.log(data.select)
          await financepcsetContractingParty({ fdoc_id: id, contracting_party: data.select })
          message.success('修改成功')
          reload()
        } catch (error) {
          console.log(error)
        }
      },
      onVisibleChange: (val) => {
        if (val) {
          handleVisibleChange(payment_type)
        }
      }
    },
    { default: () => h(Button, { size: 'small' }, () => payment_type || '请选择') }
    //
  )
}
