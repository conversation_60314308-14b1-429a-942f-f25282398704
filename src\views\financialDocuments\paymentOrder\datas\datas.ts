import type { BasicColumn, FormSchema } from '/@/components/Table'
import { determineStatus, financeExamine, getClauseOptions, SupervisorExamine } from './fn'
import { createVNode, h, ref } from 'vue'
import { formateerNotCurrency } from '/@/utils/erp/formatterPrice'
import { getCreatorList } from '/@/api/financialDocuments/public'
import { getClientList, getErpSupplier } from '/@/api/commonUtils'
import dayjs from 'dayjs'
import { useSaleOrderStore } from '/@/store/modules/saleOrder'
import { getDeptSelectTree } from '/@/api/admin/dept'
import { useRender } from '/@/components/Table/src/hooks/useRender'
import { paymentType, urgentLevel, determineType, contractingParty } from '/@/views/financialDocuments/common'
import { ApiPopover } from '/@/components/ApiPopover'
import { getWorkList } from '/@/api/commonUtils'
import { Descriptions, DescriptionsItem } from 'ant-design-vue'
import { useUserStoreWithOut } from '/@/store/modules/user'
import { customRenderIs } from '/@/views/baseData/supplier/datas/datas'
import { sub } from '/@/utils/math'
import { cpgetList } from '/@/api/erp/purchaseOrder'
import { GET_STATUS_SCHEMA } from '/@/const/status'
import { CopyOutlined } from '@ant-design/icons-vue'
import { useMessage } from '/@/hooks/web/useMessage'
import Decimal from 'decimal.js'
const saleOrderStore = useSaleOrderStore()
const pathname = window.location.pathname
export const tableRef = ref()
const { createMessage } = useMessage()

const status_schema = GET_STATUS_SCHEMA([
  { label: '未付款', value: 0 },
  { label: '已付款', value: 1 },
  { label: '已结束', value: 2 }
])

/** 筛选 */
export const searchFromSchemas: FormSchema[] = [
  status_schema,
  {
    field: 'collection_at',
    label: '付款日期',
    component: 'SingleRangeDate',
    componentProps: {
      style: {
        width: '100%'
      },
      allowEmpty: [true, true]
    }
  },
  {
    field: 'created_at',
    label: '创建日期',
    component: 'SingleRangeDate',
    componentProps: {
      style: {
        width: '100%'
      },
      allowEmpty: [true, true]
    }
  },
  {
    field: 'check_at',
    label: '审批日期',
    component: 'SingleRangeDate',
    componentProps: {
      style: {
        width: '100%'
      },
      allowEmpty: [true, true]
    }
  },
  {
    field: 'strid',
    label: '付款单号',
    component: 'Input'
  },
  {
    field: 'source_uniqid',
    label: '销售单号',
    component: 'Input'
  },

  {
    field: 'supplier_id',
    label: '供应商',
    component: 'PagingApiSelect',
    componentProps: () => {
      return {
        api: getErpSupplier,
        searchMode: true,
        pagingMode: true,
        returnParamsField: 'id',
        selectProps: {
          fieldNames: { key: 'key', value: 'id', label: 'name' },
          optionFilterProp: 'name',
          showSearch: true,
          placeholder: '请选择',
          allowClear: true
        },
        resultField: 'items',
        itEmpty: true
      }
    }
  },
  {
    field: 'client_id',
    label: '客户',
    component: 'ApiSelect',
    componentProps: {
      api: getClientList,
      selectProps: {
        fieldNames: { value: 'id', label: 'name' },
        optionFilterProp: 'name',
        showSearch: true,
        placeholder: '请选择',
        allowClear: true
      },
      itEmpty: true,
      resultField: 'items'
    }
  },
  {
    field: 'dept_ids',
    label: '部门',
    // required: true,
    component: 'ApiTreeSelect',
    componentProps: {
      api: getDeptSelectTree,
      immediate: false,
      lazyLoad: true,
      maxTagCount: 3,
      treeCheckable: true,
      multiple: true,
      treeSelectProps: {
        treeDataSimpleMode: true,
        fieldNames: { children: 'children', key: 'key', value: 'id', label: 'name' },
        treeCheckable: true,
        showCheckedStrategy: 'SHOW_ALL',
        treeDefaultExpandAll: true,
        showSearch: true,
        treeLine: {
          showLeafIcon: false
        },

        filterTreeNode: (search, item) => {
          if (item.name) return item.name.toLowerCase().indexOf(search.toLowerCase()) >= 0
          return false
        }
      }
    }
  },
  {
    field: 'creator',
    label: '创建人',
    component: 'PagingApiSelect',
    componentProps: {
      api: getCreatorList,
      searchMode: true,
      pagingMode: true,
      resultField: 'items',
      returnParamsField: 'id',
      selectProps: {
        fieldNames: { key: 'id', value: 'id', label: 'name' },
        showSearch: true,
        placeholder: '请选择',
        optionFilterProp: 'name',
        allowClear: true
      }
    },
    itemProps: {
      validateTrigger: 'blur'
    }
  },
  {
    field: 'processor',
    label: '关联人',
    component: 'PagingApiSelect',
    componentProps: {
      api: getCreatorList,
      searchMode: true,
      pagingMode: true,
      resultField: 'items',
      returnParamsField: 'id',
      selectProps: {
        fieldNames: { key: 'id', value: 'id', label: 'name' },
        showSearch: true,
        placeholder: '请选择',
        optionFilterProp: 'name',
        allowClear: true
      }
    },
    itemProps: {
      validateTrigger: 'blur'
    }
  },
  // {
  //   field: 'status',
  //   label: '状态',
  //   component: 'Select',
  //   componentProps: {
  //     options: [
  //       { label: '未付款', value: 0 },
  //       { label: '已付款', value: 1 },
  //       { label: '已结束', value: 2 }
  //     ]
  //   }
  // },
  {
    field: 'is_ticket',
    label: '是否开票',
    component: 'Select',
    componentProps: {
      options: [
        { label: '否', value: 0 },
        { label: '是', value: 1 }
      ]
    }
  },
  {
    field: 'ticket',
    label: '开票类型',
    component: 'Select',
    componentProps: {
      options: [
        { label: '增值税专用发票', value: '增值税专用发票' },
        { label: '增值税普通发票', value: '增值税普通发票' }
      ]
    }
  },
  {
    field: 'Invoicing_is_self',
    label: '开票主体',
    component: 'Select',
    componentProps: {
      options: [
        { label: '自己', value: 1 },
        { label: '其他', value: 0 }
      ]
    }
  },
  {
    field: 'urgent_level',
    label: '紧急状态',
    component: 'Select',
    componentProps: {
      options: [
        { label: '一般', value: 1 },
        { label: '紧急', value: 2 }
      ]
    }
  },
  {
    field: 'clause',
    label: '款单类型',
    component: 'Select',
    componentProps: {
      options: getClauseOptions()
    }
  },
  {
    field: 'payment_type',
    label: '款项类型',
    component: 'Select',
    componentProps: {
      options: [
        { label: '定金', value: 1 },
        { label: '最后一笔款', value: 2 },
        { label: '全款', value: 3 }
      ]
    }
  },
  {
    field: 'w_strid',
    label: '款项单号',
    component: 'Input',
    helpMessage: '详情内的单号'
  },

  {
    field: 'account_name',
    label: '收款人名称',
    component: 'Input'
  },
  {
    field: 'is_checks',
    label: '财务审批',
    component: 'Select',
    componentProps: {
      mode: 'multiple',
      options: [
        { label: '待审批', value: 0 },
        { label: '已审批', value: 1 },
        { label: '驳回', value: 2 }
      ]
    }
  },
  {
    field: 'incharge_status',
    label: '主管审批',
    component: 'Select',
    componentProps: {
      options: [
        { label: '待审批', value: 0 },
        { label: '已审批', value: 1 }
      ]
    },
    ifShow: pathname !== '/s/'
  },
  {
    field: 'amount',
    label: '应付金额',
    component: 'Input',
    slot: 'amount'
  },
  {
    field: 'remark',
    label: '付款备注',
    component: 'Input'
  },
  {
    field: 'g_remark',
    label: '携带付款备注',
    component: 'Input'
  },
  {
    field: 'contracting_party',
    label: '我司签约主体',
    component: 'PagingApiSelect',
    componentProps: {
      api: cpgetList,
      selectProps: {
        fieldNames: { key: 'key', value: 'name', label: 'name' },
        showSearch: true,
        placeholder: '请选择',
        allowClear: true,
        optionFilterProp: 'name'
      },
      resultField: 'items'
    }
  }
]

/* 付款单 */
export function columnsFn(): BasicColumn[] {
  const userStore = useUserStoreWithOut()
  return [
    {
      title: '付款单号',
      dataIndex: 'strid',
      width: 200,
      resizable: true
    },
    {
      title: '款单类型',
      dataIndex: 'clause',
      width: 100,
      resizable: true,
      customRender: ({ text }) => {
        return useRender.renderTag(saleOrderStore.orderType[text])
      }
    },
    {
      title: '紧急状态',
      dataIndex: 'urgent_level',
      width: 100,
      resizable: true,
      helpMessage: '未关联流水可以点击修改紧急状态！',
      customRender: ({ text, record }) => {
        return text ? urgentLevel(record.id, text, record.status !== 2, 2, tableRef.value?.tableAction.reload ?? (() => {})) : '-'
      }
    },
    {
      title: '款项类型',
      dataIndex: 'payment_type',
      width: 100,
      resizable: true,
      helpMessage: '点击修改款项类型！',
      customRender: ({ text, record }) => {
        // 当前人角色
        const roleValue = userStore.getUserInfo?.roleValue
        // 可以修改的角色
        const changeRoles = ['treasurer', 'financia_review', 'finance', 'super_admin', 'developer', 'cashier']
        // 判断本人的角色是财务相关、开发者、超管其中之一才能改
        // const isChange = changeRoles.includes(roleValue) ? record.clause == 2 : record.clause == 2 && record.is_check == 0
        const isChange = changeRoles.includes(roleValue) ? record.clause == 2 : false
        return text ? paymentType(record.id, text, isChange, 2, tableRef.value?.tableAction.reload ?? (() => {})) : '-'
      }
    },
    {
      title: '创建日期',
      dataIndex: 'created_at',
      width: 100,
      resizable: true,
      customRender: ({ text }) => {
        return text ? text : '-'
      }
    },

    {
      title: '客户',
      dataIndex: 'client_name',
      width: 100,
      resizable: true,
      customRender: ({ text }) => {
        return text ? text : '-'
      }
    },
    {
      title: '供应商',
      dataIndex: 'supplier_name',
      width: 100,
      resizable: true,
      customRender: ({ record }) => {
        return record.supplier_name ? record.supplier_name : '-'
      },
      sorter: (a, b) => {
        return a.supplier_id - b.supplier_id
      }
    },
    {
      title: '我司签约主体',
      dataIndex: 'contracting_party',
      width: 250,
      resizable: true,
      customRender: ({ text, record }) => {
        // 当前人角色
        const roleValue = userStore.getUserInfo?.roleValue
        console.log(roleValue)

        // 可以修改的角色
        const changeRoles = ['treasurer', 'financia_review', 'finance', 'super_admin', 'developer', 'cashier']
        // 判断本人的角色是财务相关、开发者、超管其中之一才能改
        // const isChange = changeRoles.includes(roleValue) ? record.clause == 2 : record.clause == 2 && record.is_check == 0
        const isChange = changeRoles.includes(roleValue) ? record.incharge_status == 1 && record.is_check == 0 : false
        return text ? contractingParty(record.id, text, isChange, tableRef.value?.tableAction.reload ?? (() => {})) : '-'
      }
    },
    {
      title: '账号',
      dataIndex: 'account',
      width: 150,
      resizable: true,
      customRender: ({ text }) => {
        return text ? text : '-'
      }
    },
    {
      title: '开户行',
      dataIndex: 'bank',
      width: 100,
      resizable: true,
      customRender: ({ text }) => {
        return text ? text : '-'
      }
    },
    {
      title: '收款人名称',
      dataIndex: 'account_name',
      width: 100,
      resizable: true,
      ifShow: true
    },
    // {
    //   title: '同状态账号数',
    //   dataIndex: 'notdf_count',
    //   width: 100,
    //   resizable: true,
    //   ifShow: true
    // },
    // {
    //   title: '账号',
    //   dataIndex: 'account',
    //   width: 100,
    //   resizable: true,
    //   ifShow: false
    // },
    {
      title: '应付金额',
      dataIndex: 'amount',
      width: 100,
      resizable: true,
      customRender: ({ text }) => {
        // return text ? formateerNotCurrency.format(Math.ceil(text * 100) / 100) : '0.00'
        return text ? formateerNotCurrency.format(text) : '0.00'
      }
    },
    {
      title: '本次应付金额',
      dataIndex: 'amount_cost',
      width: 100,
      resizable: true,
      customRender: ({ text }) => {
        return text ? formateerNotCurrency.format(text, 2) : '0.00'
      }
    },
    {
      title: '本次已付金额',
      dataIndex: 'amount_paid',
      width: 100,
      resizable: true,
      customRender: ({ text }) => {
        return text ? formateerNotCurrency.format(text, 2) : '0.00'
      }
    },
    {
      title: '携带付款备注',
      dataIndex: 'g_remark',
      width: 250,
      resizable: true,
      helpMessage: '生成付款单时携带的备注！',
      customRender: ({ text }) => {
        return text ? text : '-'
      }
    },
    {
      title: '部门',
      dataIndex: 'department',
      width: 100,
      resizable: true,
      customRender: ({ text }) => {
        return text ? text : '-'
      }
    },
    {
      title: '创建人',
      dataIndex: 'creator_name',
      width: 100,
      resizable: true,
      customRender: ({ text }) => {
        return text ? text : '-'
      }
    },
    {
      title: '关联人',
      dataIndex: 'processor_name',
      width: 100,
      resizable: true,
      customRender: ({ record }) => {
        return record.processor_name ? record.processor_name : '-'
      }
    },
    {
      title: '财务审核人',
      dataIndex: 'financer_name',
      width: 150,
      resizable: true
    },
    {
      title: '财务审核备注',
      dataIndex: 'check_remark',
      width: 250,
      resizable: true,
      customRender: ({ text }) => {
        return text ? text : '-'
      }
    },
    {
      title: '审批日期',
      dataIndex: 'check_at',
      width: 100,
      resizable: true,
      customRender: ({ text }) => {
        return text ? dayjs(text) : '-'
      }
    },
    {
      title: '付款日期',
      dataIndex: 'collection_at',
      width: 100,
      resizable: true,
      customRender: ({ text }) => {
        return text ? text : '-'
      }
    },
    {
      title: '付款备注',
      dataIndex: 'remark',
      width: 500,
      resizable: true,
      customRender: ({ text }) => {
        return text ? text : '-'
      }
    },
    {
      title: '财务审批',
      dataIndex: 'is_check',
      width: 100,
      resizable: true,
      customRender: ({ text }) => {
        return financeExamine(text)
      }
    },
    {
      title: '主管审批',
      dataIndex: 'incharge_status',
      width: 100,
      resizable: true,
      customRender: ({ text }) => {
        return SupervisorExamine(text)
      },
      defaultHidden: pathname == '/s/'
    },
    {
      title: '状态',
      dataIndex: 'status',
      width: 100,
      customRender: ({ text }) => {
        return determineStatus(text)
      }
    },

    {
      title: '是否开票',
      dataIndex: 'is_ticket',
      width: 120,
      resizable: true,
      customRender: ({ text }) => {
        return customRenderIs(text, ['是', '否'])
      }
    },
    {
      title: '开票类型',
      dataIndex: 'ticket',
      width: 80,
      resizable: true
    },
    {
      title: '开票税点',
      dataIndex: 'tax_point',
      width: 100,
      resizable: true,
      customRender: ({ text }) => {
        return text && text + '%'
      }
    },
    {
      title: '开票主体',
      dataIndex: 'Invoicing_is_self',
      width: 100,
      resizable: true,
      customRender: ({ value }) => customRenderIs(value, ['自己', '其他'])
    },
    {
      title: '外币总额',
      dataIndex: 'foreign_currency_amount',
      width: 100,
      defaultHidden: true,
      resizable: true
    },
    {
      title: '附件',
      dataIndex: 'files',
      width: 100,
      resizable: true
    }
  ]
}

/** 关联流水顶部 */
export const updateFormSchema: FormSchema[] = [
  {
    field: 'processor',
    label: '关联人',
    component: 'PagingApiSelect',
    required: true,
    componentProps: {
      api: getCreatorList,
      searchMode: true,
      pagingMode: true,
      resultField: 'items',
      returnParamsField: 'id',
      selectProps: {
        fieldNames: { key: 'id', value: 'id', label: 'name' },
        showSearch: true,
        placeholder: '请选择',
        optionFilterProp: 'name',
        allowClear: true
      }
    },
    itemProps: {
      validateTrigger: 'blur'
    }
  },
  {
    field: 'collection_at',
    label: '付款日期',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    },
    required: true
    // dynamicDisabled: true
  },
  {
    field: 'amount',
    label: '应付金额',
    component: 'Input',
    componentProps: {
      prefix: '￥',
      suffix: 'RMB'
    },
    defaultValue: 0,
    dynamicDisabled: true
  },
  {
    field: 'amount_cost',
    label: '本次应付金额',
    component: 'Input',
    componentProps: {
      prefix: '￥',
      suffix: 'RMB'
    },
    defaultValue: 0,
    dynamicDisabled: true
  }
]

/** 关联任务详情 */
export const detalisColumns: BasicColumn[] = addWorkListColumnsFn()

/** 关联流水详情 */
export const fundDetalisColumns: BasicColumn[] = addFundListColumnsFn()

/** 关联任务列表(编辑) */
export function addWorkListColumnsFn(handleFn?: any, type?: number, handlePreview): any {
  const workListColumns: BasicColumn[] = [
    {
      title: 'ID',
      dataIndex: 'work_id',
      width: 50,
      resizable: true,
      ifShow: false
    },
    {
      title: '单号',
      dataIndex: 'strid',
      width: 200,
      resizable: true,
      customRender: ({ record, text }) => {
        return h(
          'div',
          {
            style: {
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center' // 添加居中对齐
            }
          },
          [
            h(
              ApiPopover,
              {
                disabled: record.basic_work_id ? false : true,
                api: getWorkList,
                params: {
                  id: record.basic_work_id
                }
              },
              {
                popoverDefault: () =>
                  h(
                    'div',
                    {
                      onClick: () => {
                        handleFn(record)
                      },
                      style: {
                        color: 'red',
                        cursor: 'pointer',
                        marginRight: '8px',
                        textAlign: 'center' // 文本居中
                      }
                    },
                    text
                  ),
                popoverContent: (val) => {
                  return h(Descriptions, { bordered: true, size: 'small' }, () => [
                    h(DescriptionsItem, { label: '销售金额' }, () => val?.data?.items[0]?.receivable ?? '0.00'),
                    h(DescriptionsItem, { label: '已收金额' }, () => val?.data?.items[0]?.received ?? '0.00')
                  ])
                }
              }
            ),
            h(
              'span',
              {
                onClick: () => {
                  // 复制文本到剪贴板
                  if (text) {
                    navigator.clipboard
                      .writeText(text)
                      .then(() => {
                        createMessage.success('复制成功')
                      })
                      .catch(() => {
                        createMessage.error('复制失败')
                      })
                  }
                },
                style: {
                  cursor: 'pointer',
                  marginLeft: '5px',
                  color: '#1890ff'
                },
                title: '复制'
              },
              [h(CopyOutlined)]
            )
          ]
        )
      }
    },
    {
      title: '类型',
      dataIndex: 'type',
      width: 100,
      resizable: true,
      customRender: ({ text }) => {
        return determineType(text)
      }
    },
    {
      title: '部门',
      dataIndex: 'department',
      width: 100,
      resizable: true,
      customRender: ({ text }) => {
        return text ? text : '-'
      }
    },
    {
      title: type == 11 ? '退款金额' : type == 8 ? '其他支出金额' : '采购金额',
      dataIndex: 'cost',
      width: 100,
      resizable: true,
      customRender: ({ text }) => {
        // return text ? formateerNotCurrency.format(text) : '0.00'
        return text ?? 0
      }
    },
    {
      title: '汇率',
      dataIndex: 'exchange_rate',
      width: 100,
      resizable: true
    },
    {
      title: '币别',
      dataIndex: 'currency',
      width: 100,
      resizable: true
    },
    {
      title: '外汇金额',
      dataIndex: 'foreign_currency_amount',
      defaultHidden: pathname == '/s/',
      width: 150,
      resizable: true
    },
    {
      title: '实际已付金额',
      dataIndex: 'paid_actual',
      width: 100,
      resizable: true,
      customRender: ({ text }) => {
        // return text ? formateerNotCurrency.format(text, 2) : '0.00'
        return text ?? 0
      }
    },
    {
      title: '已付金额',
      dataIndex: 'paid',
      width: 100,
      resizable: true,
      customRender: ({ text }) => {
        // return text ? formateerNotCurrency.format(text, 2) : '0.00'
        return text ?? 0
      }
    },
    {
      title: '未付金额',
      dataIndex: 'no_amount',
      width: 100,
      customRender: ({ record }) => {
        // return record.cost - record.paid ? formateerNotCurrency.format(record.cost - record.paid, 2) : '0.00'
        return sub(record.cost ?? 0, record.paid ?? 0)
      }
    },
    {
      title: '本次应付金额',
      dataIndex: 'amount',
      width: 100,
      resizable: true,
      customRender: ({ text }) => {
        // return text ? formateerNotCurrency.format(text, 2) : '0.00'
        return text ?? 0
      }
    },
    {
      title: '当前需付款金额',
      dataIndex: 'current_amount',
      width: 100,
      customRender: ({ text }) => {
        // return text ? formateerNotCurrency.format(text, 2) : '0.00'
        return text ?? 0
      }
    },
    {
      title: '查看附件',
      dataIndex: 'files',
      width: 150,
      customRender: ({ text }) => {
        const vNode = createVNode(
          'div',
          null,
          text?.map((url, index) => {
            const link = createVNode(
              'a',
              {
                target: '_blank',
                onClick: (event) => {
                  event.stopPropagation() // 取消默认行为
                  // window.open(url, '_blank') // 打开链接
                  handlePreview(url, event)
                }
              },
              `附件${index + 1}`
            )
            return index < text.length - 1
              ? [link, '、 '] // 添加逗号
              : link
          })
        )
        return text && text?.length !== 0 ? vNode : '-'
      }
    },
    {
      title: '销售总价',
      dataIndex: 'sales_price',
      width: 100,
      resizable: true,
      customRender: ({ text }) => {
        // return text ? formateerNotCurrency.format(text) : '0.00'
        return text ?? 0
      },
      ifShow: type == 4 // 4 是采购
    },
    {
      title: '利润率',
      dataIndex: 'rate',
      editComponent: 'InputNumber',
      width: 100,
      customRender: ({ text }) => {
        // return text ? `${formateerNotCurrency.format(text)}%` : '0%'
        return text ? `${text}%` : '0%'
      },
      ifShow: type == 4
    }
  ]
  return workListColumns
}

/** 关联流水列表 */
export function addFundListColumnsFn(editChange?: Function): any {
  const fundListColumns: BasicColumn[] = [
    {
      title: 'ID',
      dataIndex: 'id',
      width: 50,
      resizable: true,
      customRender: ({ text }) => {
        return text ? text : '-'
      },
      ifShow: false
    },
    {
      title: '流水单号',
      dataIndex: 'strid',
      width: 200,
      resizable: true
    },

    {
      title: '付款资金资料',
      dataIndex: 'from_plaform',
      width: 100,
      resizable: true,
      customRender: ({ record }) => {
        return record.from_plaform ? record.from_plaform : '-'
      }
    },

    {
      title: '收款资金资料',
      dataIndex: 'to_plaform',
      width: 100,
      resizable: true,
      customRender: ({ record }) => {
        return record.to_plaform ? record.to_plaform : '-'
      }
    },

    {
      title: '手续费',
      dataIndex: 'fee',
      width: 100,
      resizable: true,
      customRender: ({ record }) => {
        return record.fee ? record.fee : '0.00'
      }
    },
    {
      title: '汇率',
      dataIndex: 'rate',
      width: 100,
      resizable: true,
      customRender: ({ text }) => {
        return text ? formateerNotCurrency.format(Number(text)) : '0.00'
      }
    },
    {
      title: '币种',
      dataIndex: 'from_currency',
      width: 100,
      resizable: true
    },
    {
      title: '外汇金额',
      dataIndex: 'fg_amount',
      width: 100,
      defaultHidden: pathname == '/s/',
      resizable: true,
      customRender: ({ text }) => {
        return text ? formateerNotCurrency.format(text) : '0.00'
      }
    },
    {
      title: '外汇剩余金额',
      dataIndex: 'fg_amount_left',
      width: 100,
      resizable: true,
      defaultHidden: pathname == '/s/',
      customRender: ({ text }) => {
        return text ? formateerNotCurrency.format(text) : '0.00'
      }
    },
    {
      title: '金额',
      dataIndex: 'amount',
      width: 100,
      resizable: true,
      customRender: ({ text }) => {
        return text ? formateerNotCurrency.format(text) : '0.00'
      }
    },
    {
      title: '剩余金额',
      dataIndex: 'amount_left',
      width: 100,
      resizable: true,
      customRender: ({ text, record }) => {
        return record.cloneAmountLeft
          ? formateerNotCurrency.format(Number(record.cloneAmountLeft) - Number(record.amount_allot))
          : formateerNotCurrency.format(text)
      }
    },
    {
      title: '本次外汇分配金额',
      dataIndex: 'foreign_currency_amount',
      editComponent: 'InputNumber',
      width: 100,
      resizable: true,
      editRow: true,
      defaultHidden: pathname == '/s/',
      editDynamicDisabled(record) {
        return ['人民币', 'CNY'].includes(record.record.from_currency) && pathname !== '/s/' ? true : false
      },
      editComponentProps: ({ record }) => {
        return {
          min: 0,
          max: record.fg_amount,
          onChange: () => {
            record.amount_allot = new Decimal(record.foreign_currency_amount).times(record.rate).toDecimalPlaces(2).toNumber()
            console.log()
          }
        }
      },
      editRender: ({ text }) => {
        return text ? formateerNotCurrency.format(Number(text)) : '0.00'
      }
      // editRule: (text: string) => {
      //   if (Number(text) < 0) {
      //     return Promise.resolve('请输入大于0的金额！')
      //   } else {
      //     return Promise.resolve('')
      //   }
      // }
    },
    {
      title: '本次分配金额',
      dataIndex: 'amount_allot',
      editComponent: 'InputNumber',
      width: 100,
      resizable: true,
      editRow: true,
      editComponentProps: ({ record }) => {
        return {
          min: 0,
          precision: 2,
          max: record.amount_left,
          onChange: () => {
            editChange!()
          }
        }
      },

      editRender: ({ text }) => {
        return text ? formateerNotCurrency.format(Number(text)) : '0.00'
      },
      editDynamicDisabled(record) {
        return !['人民币', 'CNY'].includes(record.record.from_currency) && pathname !== '/s/' ? true : false
      }
    },
    {
      title: '附件',
      dataIndex: 'files',
      width: 100,
      slots: 'files',
      resizable: true
    }
  ]
  return fundListColumns
}
