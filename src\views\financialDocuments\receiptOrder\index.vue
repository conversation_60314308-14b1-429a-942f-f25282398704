<template>
  <div>
    <BasicTable :data-cachekey="routePath" :ref="(el) => (tableRef = el)" @register="registerTable">
      <template #toolbar>
        <Button type="primary" v-if="hasPermission(582)" @click="handleExport" :loading="exporting">收款单导出</Button>
        <Button type="primary" v-if="hasPermission(304)" @click="handleBatchRelate" :loading="exporting">批量关联</Button>
      </template>
      <template #bodyCell="{ text, column, record }">
        <template v-if="column.key === 'action'">
          <TableAction :actions="createActions(record)" :drop-down-actions="createDropDownActions(record)" />
        </template>
        <template v-if="column.key === 'files'">
          <TableImg :size="60" :simpleShow="true" :imgList="text" />
        </template>
        <template v-if="column.key === 'client_name'">
          <Badge :count="record.no_receipt_num">
            <div style="padding: 10px">{{ record.client_name || '-' }}</div>
          </Badge>
        </template>
      </template>
      <template #form-receivable="{ model }">
        <FormItemRest>
          <div style="display: flex; align-items: center">
            <InputNumber
              v-model:value="model.amount_ans_start"
              valueFormat="YYYY-MM-DD 00:00:00"
              placeholder="应收金额初始值"
              style="height: 35px"
              :precision="4"
            />
            <span class="iconify" data-icon="ant-design:swap-outlined" style="width: 32px; padding: 0 3px 0 3px"></span>
            <InputNumber
              v-model:value="model.amount_ans_end"
              valueFormat="YYYY-MM-DD 23:59:59"
              placeholder="应收金额最大值"
              style="height: 35px"
              :precision="4"
            />
          </div>
        </FormItemRest>
      </template>
      <template #expandedRowRender="{ record, expanded }">
        <div class="expand-row-table">
          <BasicTable
            v-if="expanded"
            :ref="(el) => (expandedRowRefs[record.id] = el)"
            :api="(params) => getReceiptOrderDetails({ ...params, id: record.id })"
            rowKey="id"
            :showIndexColumn="true"
            :scroll="{ y: 300, x: 0 }"
            :afterFetch="(res) => handleAfterFetch(res, expandedRowRefs[record.id])"
          />
        </div>
      </template>

      <template #footer>
        <div class="footer">
          <span> 应收金额合计： {{ formateerNotCurrency.format(totalAmount) }} </span>
          <span>本次应收金额合计：{{ formateerNotCurrency.format(totalAmountAns) }}</span>
          <span>本次已收金额合计：{{ formateerNotCurrency.format(totalAmountRec) }}</span>
        </div>
      </template>
    </BasicTable>

    <ReceiptOrderDrawer @register="registerDrawer" @success="handleSuccess" />
    <DetailsDrawer @register="registerDetailsDrawer" />
    <ExaminePaymentOrder @register="registerExamineDrawer" @success="handleSuccess" />
    <BatchRelateDrawer @register="registerBatchDrawer" @success="handleSuccess" />
  </div>
</template>

<script setup lang="tsx">
import {
  financercsetSinoChinaStatus,
  getReceiptOrderDetails,
  getReceiptOrderList,
  GetReceiptOrderListExcel,
  setFollowRemark
} from '/@/api/financialDocuments/receiptOrder'
import { columnsFn, searchFromSchemas, childrenColumns, tableRef } from './datas/datas'
import ReceiptOrderDrawer from './components/ReceiptOrderDrawer.vue'
import { deleteReceiptOrder } from '/@/api/financialDocuments/receiptOrder'
import ExaminePaymentOrder from './components/ExaminePaymentOrder.vue'
import DetailsDrawer from './components/DetailsDrawer.vue'
import BatchRelateDrawer from './components/BatchRelateDrawer.vue'
import { BasicTable, useTable, TableAction, TableImg } from '/@/components/Table'
import type { ActionItem, EditRecordRow } from '/@/components/Table'
import { useDrawer } from '/@/components/Drawer'
import { usePermission } from '/@/hooks/web/usePermission'
import { useUserStore } from '/@/store/modules/user'
import { message, InputNumber, Form, Badge, Textarea, Button } from 'ant-design-vue'
import { ref } from 'vue'
import { isArray } from 'lodash-es'
import { formateerNotCurrency } from '/@/utils/erp/formatterPrice'
import { add } from '/@/utils/math'
import { useRoute } from 'vue-router'
import { useMessage } from '/@/hooks/web/useMessage'
import { NEW_STATUS_FORMCONFIG } from '/@/const/status'

const exporting = ref<boolean>(false)
const { createMessage } = useMessage()
const route = useRoute()
const { path: routePath } = route
const FormItemRest = Form.ItemRest
const expandedRowRefs = ref({})
const userStore = useUserStore()
const { hasPermission } = usePermission()
const totalAmount = ref<number>(0)
const totalAmountAns = ref<number>(0)
const totalAmountRec = ref<number>(0)
// 注册批量关联弹窗
const [registerBatchDrawer, { openDrawer: openBatchDrawer }] = useDrawer()

/** 注册关联流水抽屉 */
const [registerDrawer, { openDrawer, setDrawerProps }] = useDrawer()

/** 注册详情抽屉 */
const [registerDetailsDrawer, { openDrawer: openDetailsDrawer, setDrawerProps: setDetailsDrawerProps }] = useDrawer()

/** 注册收款单列表 */
const [registerTable, { reload, getSelectRows, clearSelectedRowKeys, getForm, setLoading }] = useTable({
  title: '收款单',
  showIndexColumn: false,
  columns: columnsFn(),
  api: getReceiptOrderList,
  beforeFetch: (params) => {
    const fieldsArr = ['collection_at_start', 'collection_at_end']
    for (const key of Object.keys(params)) {
      if (fieldsArr.includes(key) && isNaN(Date.parse(params[key]))) {
        params[key] = void 0
      }
    }
    return params
  },
  afterFetch: (tableData) => {
    totalAmount.value = 0
    totalAmountAns.value = 0
    totalAmountRec.value = 0
    tableData.forEach((item) => {
      totalAmount.value = add(totalAmount.value, Number(item.amount), 4)
      totalAmountAns.value = add(totalAmountAns.value, Number(item.amount_ans), 4)
      totalAmountRec.value = add(totalAmountRec.value, Number(item.amount_rec), 4)
    })
  },
  actionColumn: {
    width: 300,
    title: '操作',
    dataIndex: 'action'
  },
  showTableSetting: true,
  useSearchForm: true,
  rowKey: 'id',
  formConfig: {
    ...NEW_STATUS_FORMCONFIG,
    schemas: searchFromSchemas,
    fieldMapToTime: [
      ['collection_at', ['collection_at_start', 'collection_at_end'], ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59']],
      ['created_at', ['created_at_start', 'created_at_end'], ['YYYY-MM-DD HH:mm:ss', 'YYYY-MM-DD HH:mm:ss']],
      ['fund_at', ['fund_at_start', 'fund_at_end'], ['YYYY-MM-DD HH:mm:ss', 'YYYY-MM-DD HH:mm:ss']]
    ]
  },
  rowSelection: {
    getCheckboxProps: (record) => {
      return { disabled: record.status == 1 || record.is_check == 2 }
    }
  }
})

// onMounted(() => {
//   nextTick(() => {
//     setColumns(columnsFn(reload))
//   })
// })

/** 操作按钮 */
function createActions(record: EditRecordRow): Recordable[] {
  let editButtonList: ActionItem[] = [
    {
      icon: 'ant-design:plus-square-outlined',
      label: '关联流水',
      onClick: handleUpdate.bind(null, record),
      disabled:
        (record.status == 1 &&
          (![1, 2].includes(Number(userStore.getUserInfo?.roleId)) ||
            !(userStore.getUserInfo?.roleId === 5 && userStore.getUserInfo?.userId === record.creator))) ||
        record.is_check == 2,
      ifShow: hasPermission([98])
    },
    {
      label: '设置跟进备注',
      popConfirm: {
        title: (
          <div class="w-100">
            <Textarea key={record.id} autoSize={false} v-model:value={record.followRemark} placeholder="请输入跟进备注" allow-clear />
          </div>
        ),
        placement: 'left',
        confirm: handleSetFollowRemark.bind(null, record),
        disabled: record.status == 1
      },
      ifShow: hasPermission(549)
    }
  ]

  return editButtonList
}

function createDropDownActions(record: EditRecordRow): ActionItem[] {
  return [
    {
      icon: 'ant-design:eye-outlined',
      label: '详情',
      onClick: handleDetail.bind(null, record),
      ifShow: hasPermission([97])
    },
    {
      icon: 'ant-design:delete-outlined',
      color: 'error',
      label: '删除',
      popConfirm: {
        title: '是否确认删除',
        placement: 'left',
        confirm: handleDelete.bind(null, record),
        disabled: record.clause == 3 ? true : ![0, 3].includes(record.status) || [2].includes(record.is_check)
      },
      // disabled: ![0, 3].includes(record.status) || [2].includes(record.is_check),
      ifShow: hasPermission([99])
    },
    {
      icon: 'ant-design:file-search-outlined',
      color: 'error',
      label: '财务驳回',
      onClick: handleExamine.bind(null, record, false),
      disabled: ![0, 3].includes(record.status) || [2].includes(record.is_check)
      // ifShow: hasPermission([284]) && [3, 6].includes(record.clause)
    },
    {
      label: 'sinochina特批',
      onClick: setSinoChinaStatus.bind(null, record),
      disabled:
        (record.status == 1 &&
          (![1, 2].includes(Number(userStore.getUserInfo?.roleId)) ||
            !(userStore.getUserInfo?.roleId === 5 && userStore.getUserInfo?.userId === record.creator))) ||
        record.is_check == 2,
      ifShow: hasPermission([586])
    }
  ]
}

async function handleSetFollowRemark(record) {
  try {
    if (record.status == 1) return
    record.status = 1
    const data = { id: record.id, follow_remark: record.followRemark }
    const { msg } = await setFollowRemark(data)
    if (msg === 'success') {
      createMessage.success('设置成功')
      reload()
      return
    }
    createMessage.error('设置失败！请联系管理员')
  } catch (err) {
    createMessage.error('设置失败！请联系管理员')
  }
}

/** 关联流水 */
function handleUpdate(record, e) {
  e.stopPropagation()
  openDrawer(true, {
    record
    // isUpdate: true
  })
  setDrawerProps({ title: '收款单关联流水' })
}

/** 详情 */
function handleDetail(record) {
  setDetailsDrawerProps({ title: '收款单详情' })
  openDetailsDrawer(true, {
    id: record.id
  })
}

/** 删除 */
async function handleDelete(record) {
  try {
    await deleteReceiptOrder({ id: record.id })
    message.success('删除成功！')
    reload()
  } catch (error) {
    message.error('删除失败！')
    throw new Error(`${error}`)
  }
}
/** 审批 */
const [registerExamineDrawer, { openDrawer: openExamineDrawer, setDrawerProps: setExamineDrawerProps }] = useDrawer()

function handleExamine(record, isCashier) {
  openExamineDrawer(true, {
    record,
    isCashier
  })
  setExamineDrawerProps({ title: '审批付款单' })
}
/** 成功回调 */
function handleSuccess(record) {
  reload()
  clearSelectedRowKeys()
  if (isArray(record)) {
    for (const item of record) {
      expandedRowRefs.value[item.id]?.tableAction?.reload()
    }
    return
  }
  expandedRowRefs.value[record.id]?.tableAction?.reload()
}

function handleAfterFetch(res, action) {
  action?.tableAction?.setColumns(childrenColumns(res))
  return res.work
}

function handleBatchRelate() {
  const selectRow = getSelectRows()
  if (selectRow.length <= 1) return message.error('请选择两条以上数据进行批量关联')

  // 判断关联人是否相同
  const clauseOrder = selectRow.filter((item) => item.clause === 1)
  const [firstClause] = clauseOrder
  const equalClause = clauseOrder.every((item) => item.processor === firstClause.processor)
  if (!equalClause) return message.error('请确保所有销售类型的关联人相同')

  // 判断对方付款人是否相同
  const [firstRow] = selectRow
  const { notes } = firstRow
  const allow = selectRow.every((item) => item.notes === notes)
  if (!allow) return message.error('请确保所有的收款单都是同一个对方付款人')

  const [firstcurrency] = selectRow
  const { currency } = firstcurrency
  const currencyAllow = selectRow.every((item) => item.currency === currency)
  if (!currencyAllow) return message.error('请确保所有的收款单都是同一个币种')

  openBatchDrawer(true, { selectRow })
}

async function handleExport() {
  try {
    exporting.value = true
    setLoading(true)

    const params = getForm()?.getFieldsValue()
    const response = await GetReceiptOrderListExcel({ ...params, pageSize: 10000, is_excel: 1 })
    // const blob = new Blob([response.data], { type: response.headers['content-type'] })
    const downloadLink = document.createElement('a')
    downloadLink.href = URL.createObjectURL(response)
    downloadLink.download = `收款单-${+new Date()}.xlsx`

    // 模拟点击下载链接
    downloadLink.click()

    // 清理临时资源
    URL.revokeObjectURL(downloadLink.href)
    createMessage.success('导出成功')
  } catch (err) {
    createMessage.error('导出失败')
    throw new Error(err)
  } finally {
    exporting.value = false
    setLoading(false)
  }
}

async function setSinoChinaStatus(record) {
  try {
    setLoading(true)
    await financercsetSinoChinaStatus({ fdoc_id: record.id })
    setLoading(false)
    await reload()
  } catch (e) {
    console.log(e)
  }
}
</script>

<style scoped lang="less">
.expand-row-table {
  :deep(.ant-table) {
    margin: 0 !important;
  }
}

.footer {
  font-size: 15px;
  font-weight: bold;

  span:nth-of-type(2) {
    margin-left: 7%;
  }

  span:nth-of-type(3) {
    margin-left: 7%;
  }
}
</style>
