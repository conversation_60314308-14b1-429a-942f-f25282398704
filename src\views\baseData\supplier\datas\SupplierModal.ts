import { getDept } from '/@/api/erp/systemInfo'
import { getAccountList, getDeptList } from '/@/api/commonUtils'
import { cpgetList } from '/@/api/erp/purchaseOrder'
import type { BasicColumn, FormSchema } from '/@/components/Table'
import { cloneDeep } from 'lodash-es'
export function schemasFn(status, page = 'index', Fn?): FormSchema[] {
  return [
    {
      field: 'is_disabled',
      label: '状态',
      component: 'RadioButtonGroup',
      defaultValue: 0,
      required: true,
      componentProps: {
        options: [
          { label: '启用', value: 0 },
          { label: '禁用', value: 1 }
        ]
      },
      colProps: { span: 8 },
      dynamicDisabled: status,
      ifShow: page === 'index'
    },
    {
      field: 'is_company',
      label: '公司类型',
      component: 'RadioButtonGroup',
      defaultValue: 0,
      required: true,
      componentProps: ({ formModel }) => {
        return {
          options: [
            { label: '个人', value: 0 },
            { label: '公司', value: 1 }
          ],
          onChange: async () => {
            formModel.name = ''
            formModel.credit_code = ''
            formModel.oper_name = ''
            formModel.company_status = ''
            formModel.address = ''
          }
        }
      },
      colProps: { span: 8 },
      // colProps: { span: page === 'index' ? 6 : 24 },
      dynamicDisabled: status
    },
    {
      field: 'is_open_tax',
      label: '开增值税票',
      component: 'Select',
      required: true,
      componentProps: {
        options: [
          { label: '是', value: 1 },
          { label: '否', value: 0 }
        ],
        onChange: async (value) => {
          const { is_open_ticket } = await Fn?.getFieldsValue()
          await Fn?.updateSchema([
            { field: 'tax_point', required: !!value },
            { field: 'Invoicing_is_self', required: !!is_open_ticket || !!value }
          ])

          if (page !== 'index') Fn?.clearValidate(['Invoicing_is_self', 'tax_point', 'ticket_point'])
        }
      },
      colProps: { span: 8 },
      dynamicDisabled: status
    },
    {
      field: 'is_open_ticket',
      label: '开普票',
      component: 'Select',
      required: true,
      componentProps: {
        options: [
          { label: '是', value: 1 },
          { label: '否', value: 0 }
        ],
        onChange: async (value) => {
          const formData = await Fn?.getFieldsValue()
          await Fn?.updateSchema([
            { field: 'ticket_point', required: !!value },
            { field: 'Invoicing_is_self', required: !!formData.is_open_tax || !!value }
          ])
          if (page !== 'index') Fn?.clearValidate(['Invoicing_is_self', 'tax_point', 'ticket_point'])
        }
      },
      colProps: { span: 8 },
      dynamicDisabled: status
    },
    {
      field: 'Invoicing_is_self',
      label: '开票主体',
      component: 'Select',
      componentProps: {
        options: [
          { label: '自己', value: 1 },
          { label: '其他', value: 0 }
        ]
      },
      colProps: { span: 8 },
      dynamicDisabled: status
    },
    {
      field: 'name',
      label: '供应商名称',
      component: 'Input',
      required: true,
      slot: 'names',
      colProps: { span: 8 },
      dynamicDisabled: status
    },
    {
      field: 'credit_code',
      label: '统一社会信用代码',
      itemHelpMessage: '若查询企业为中国香港企业时：返回商业登记号码',
      component: 'Input',
      dynamicDisabled: true,
      colProps: { span: 8 },
      show(renderCallbackParams) {
        return renderCallbackParams.model.is_company === 1
      }
    },
    {
      field: 'oper_name',
      label: '法定代表人姓名',
      component: 'Input',
      colProps: { span: 8 },
      dynamicDisabled: true,
      show(renderCallbackParams) {
        return renderCallbackParams.model.is_company === 1
      }
    },
    {
      field: 'company_status',
      label: '公司状态',
      component: 'Input',
      dynamicDisabled: true,
      colProps: { span: 8 },
      show(renderCallbackParams) {
        return renderCallbackParams.model.is_company === 1
      }
    },
    {
      field: 'address',
      label: '公司注册地址',
      component: 'Input',
      dynamicDisabled: true,
      colProps: { span: 8 },
      show(renderCallbackParams) {
        return renderCallbackParams.model.is_company === 1
      }
    },
    {
      field: 'contact',
      label: '联系方式',
      component: 'Input',
      required: true,
      colProps: { span: 8 },
      dynamicDisabled: status
    },
    {
      field: 'dept_id',
      label: '关联部门',
      component: 'ApiTreeSelect',
      componentProps: ({ formModel }) => {
        return {
          api: getDeptList,
          resultField: 'items',
          maxTagCount: 3,
          // treeCheckable: true,
          multiple: true,
          treeSelectProps: {
            fieldNames: {
              key: 'key',
              value: 'id',
              label: 'name'
            },
            treeDefaultExpandAll: true,
            showSearch: true,
            placeholder: '请选择',
            optionFilterProp: 'name',
            allowClear: true,
            // treeCheckable: true,
            // showCheckedStrategy: 'SHOW_ALL',
            // treeLine: {
            //   showLeafIcon: false
            // },
            filterTreeNode: (search, item) => {
              if (item.name) return item.name.toLowerCase().indexOf(search.toLowerCase()) >= 0
              return false
            },
            onSelect(_, node) {
              const newarr = handleallIds(node)
              const arr = cloneDeep(formModel.dept_id)
              formModel.dept_id = [...new Set([...arr, ...newarr])]
            }
          }
        }
      },
      colProps: { span: 8 },
      dynamicDisabled: status,
      required: true
    },
    // {
    //   field: 'account',
    //   label: '账号',
    //   component: 'Input',
    //   required: true,
    //   colProps: { span: 8 },
    //   dynamicDisabled: status
    // },
    // {
    //   field: 'account_name',
    //   label: '账号名',
    //   component: 'Input',
    //   required: true,
    //   colProps: { span: 8 },
    //   dynamicDisabled: status
    // },
    {
      field: 'tax_point',
      label: '增值税票税点',
      component: 'InputNumber',
      dynamicDisabled: status,
      defaultValue: 0,
      colProps: { span: 8 },
      componentProps: {
        precision: 2,
        placeholder: '请输入',
        min: 0,
        max: 100,
        addonAfter: '%',
        parser: (value) => value.replace('%', '')
      }
    },
    {
      field: 'ticket_point',
      label: '普票税点',
      component: 'InputNumber',
      dynamicDisabled: status,
      defaultValue: 0,
      colProps: { span: 8 },
      componentProps: {
        precision: 2,
        min: 0,
        max: 100,
        placeholder: '请输入',
        // formatter: (value) => `${value}%`,
        addonAfter: '%',
        parser: (value) => value.replace('%', '')
      }
    },
    {
      field: 'add_point',
      label: '开税票需加收税点',
      component: 'InputNumber',
      dynamicDisabled: status,
      defaultValue: 0,
      colProps: { span: 8 },
      componentProps: {
        precision: 2,
        min: 0,
        max: 100,
        placeholder: '请输入',
        // formatter: (value) => `${value}%`,
        addonAfter: '%',
        parser: (value) => value.replace('%', '')
      },
      required(renderCallbackParams) {
        return renderCallbackParams.model.is_open_tax || renderCallbackParams.model.is_open_ticket
      }
    },
    {
      field: 'inCharge',
      label: '产品部负责人',
      component: 'ApiSelect',

      componentProps: {
        api: getAccountList,
        resultField: 'items',
        selectProps: {
          fieldNames: { key: 'id', value: 'id', label: 'name' },
          showSearch: true,
          placeholder: '请选择',
          optionFilterProp: 'name',
          allowClear: true,
          disabled: status
        }
      },
      colProps: { span: 8 },
      itemProps: {
        validateTrigger: 'blur'
      }
    },
    // {
    //   field: 'bank',
    //   label: '开户行',
    //   component: 'Input',
    //   colProps: { span: 8 },
    //   dynamicDisabled: status
    // },
    {
      field: 'is_gbuilder',
      label: '是否Gbuilder供应商',
      component: 'RadioButtonGroup',
      dynamicDisabled: status,
      colProps: { span: 8 },
      defaultValue: 0,
      componentProps: {
        options: [
          { label: '是', value: 1 },
          { label: '否', value: 0 }
        ]
      }
    },
    {
      field: 'gbuilder_incharge',
      label: 'gbuilder负责人',
      component: 'PagingApiSelect',
      componentProps: {
        api: getAccountList,
        resultField: 'items',
        searchMode: true,
        pagingMode: true,
        pagingSize: 20,
        params: { is_gbuilder: 1 },
        selectProps: {
          fieldNames: { key: 'id', value: 'id', label: 'name' },
          showSearch: true,
          placeholder: '请选择',
          optionFilterProp: 'name',
          allowClear: true,
          disabled: status
        }
      },
      colProps: { span: 8 },
      itemProps: {
        validateTrigger: 'blur'
      },
      show(renderCallbackParams) {
        return renderCallbackParams.values.is_gbuilder === 1
      },
      required(renderCallbackParams) {
        return renderCallbackParams.values.is_gbuilder === 1
      },
      dynamicDisabled: status
    },
    {
      field: 'gbuilder_dept_id',
      label: 'Gbuilder部门',
      component: 'PagingApiSelect',
      componentProps: {
        api: getDept,
        params: { isgbuilder: 1, is_audit: 1 },
        resultField: 'items',
        labelField: 'name',
        valueField: 'id',
        searchMode: true,
        pagingMode: true,
        selectProps: {
          fieldNames: {
            key: 'key',
            value: 'id',
            label: 'name'
          },
          showSearch: true,
          placeholder: '请选择',
          allowClear: true
        }
      },
      show(renderCallbackParams) {
        return renderCallbackParams.values.is_gbuilder === 1
      },
      required(renderCallbackParams) {
        return renderCallbackParams.values.is_gbuilder === 1
      },
      colProps: { span: 8 },
      itemProps: {
        validateTrigger: 'blur'
      },
      dynamicDisabled: status
    },
    {
      field: 'business_license',
      label: '营业执照',
      component: 'Upload',
      slot: 'businessicense',
      colProps: { span: 8 },
      dynamicDisabled: status,
      required(renderCallbackParams) {
        return renderCallbackParams.values.is_company === 1
      }
    },
    {
      field: 'contract',
      label: '合同',
      component: 'Upload',
      slot: 'contract',
      colProps: { span: 8 },
      dynamicDisabled: status
    },
    {
      field: 'level',
      label: '供应商等级',
      component: 'Select',
      dynamicDisabled: status,
      colProps: { span: 8 },
      componentProps: {
        options: [
          {
            value: 'A',
            label: 'A'
          },
          {
            value: 'B',
            label: 'B'
          },
          {
            value: 'C',
            label: 'C'
          },
          {
            value: 'D',
            label: 'D'
          }
        ]
      }
    },
    {
      field: 'parent_company',
      label: '母公司名称',
      component: 'Input',
      colProps: { span: 8 },
      dynamicDisabled: status
    },
    // {
    //   field: 'is_public_account',
    //   label: '是否公户',
    //   component: 'Select',
    //   dynamicDisabled: status,
    //   colProps: { span: 8 },
    //   componentProps: {
    //     options: [
    //       {
    //         label: '是',
    //         value: 1
    //       },
    //       {
    //         label: '否',
    //         value: 0
    //       }
    //     ]
    //   }
    // },
    {
      field: 'deposit_is_send',
      label: '是否定金通知',
      component: 'Select',
      defaultValue: 0,
      componentProps: {
        options: [
          {
            label: '是',
            value: 1
          },
          {
            label: '否',
            value: 0
          }
        ]
      },
      colProps: { span: 8 },
      required: true,
      dynamicDisabled: status
    },
    {
      field: 'deposit_send_person',
      label: '定金通知人员',
      component: 'Input',
      show: false,
      ifShow: true
    },
    {
      // field: 'deposit_send_person',
      field: 'deposit_send_person_name',
      label: '定金通知人员',
      component: 'PagingApiSelect',
      componentProps: ({ formModel }) => {
        return {
          api: getAccountList,
          resultField: 'items',
          searchMode: true,
          pagingMode: true,
          mode: 'multiple',
          pagingSize: 20,
          returnParamsField: 'id',
          params: { is_gbuilder: 1 },
          selectProps: {
            fieldNames: { key: 'id', value: 'name', label: 'name' },
            showSearch: true,
            placeholder: '请选择',
            optionFilterProp: 'name',
            allowClear: true,
            disabled: status,
            onChange(_, shall) {
              formModel.deposit_send_person = shall.map((item) => item.id)
            }
          }
        }
      },
      colProps: { span: 8 },
      itemProps: {
        validateTrigger: 'blur'
      },
      dynamicDisabled: status,
      show(renderCallbackParams) {
        return renderCallbackParams.model.deposit_is_send === 1
      },
      required(renderCallbackParams) {
        return renderCallbackParams.model.deposit_is_send === 1
      }
    },
    {
      field: 'no_deposit_is_send',
      label: '是否尾款通知',
      component: 'Select',
      defaultValue: 0,
      componentProps: {
        options: [
          {
            label: '是',
            value: 1
          },
          {
            label: '否',
            value: 0
          }
        ]
      },
      colProps: { span: 8 },
      required: true,
      dynamicDisabled: status
    },
    {
      field: 'no_deposit_send_person',
      label: '尾款通知人员',
      component: 'PagingApiSelect',
      show: false,
      ifShow: true
    },
    {
      // field: 'no_deposit_send_person',
      field: 'no_deposit_send_person_name',
      label: '尾款通知人员',
      component: 'PagingApiSelect',
      componentProps: ({ formModel }) => {
        return {
          api: getAccountList,
          resultField: 'items',
          searchMode: true,
          pagingMode: true,
          pagingSize: 20,
          returnParamsField: 'id',
          mode: 'multiple',
          params: { is_gbuilder: 1 },
          selectProps: {
            fieldNames: { key: 'id', value: 'name', label: 'name' },
            showSearch: true,
            placeholder: '请选择',
            optionFilterProp: 'name',
            allowClear: true,
            disabled: status,
            onChange(_, shall) {
              formModel.no_deposit_send_person = shall.map((item: any) => item.id)
            }
          }
        }
      },
      colProps: { span: 8 },
      itemProps: {
        validateTrigger: 'blur'
      },
      show(renderCallbackParams) {
        return renderCallbackParams.model.no_deposit_is_send === 1
      },
      required(renderCallbackParams) {
        return renderCallbackParams.model.no_deposit_is_send === 1
      },
      dynamicDisabled: status
    },
    {
      field: 'remark',
      label: '备注',
      component: 'InputTextArea',
      colProps: { span: 8 },
      dynamicDisabled: status
    }
  ]
}

export function schemas(is_ticket?: number, ticket?: string): FormSchema[] {
  return [
    {
      field: 'is_ticket',
      label: '是否开票',
      component: 'Select',
      required: is_ticket == 1 ? true : false,
      defaultValue: is_ticket,
      ifShow: is_ticket == 1 ? true : false,
      componentProps: {
        options: [
          {
            label: '不开票',
            value: 0
          },
          {
            label: '开票',
            value: 1
          }
        ]
      }
    },
    {
      field: 'ticket',
      label: '开票类型',
      component: 'Select',
      required(renderCallbackParams) {
        return renderCallbackParams.values.is_ticket == 1 ? true : false
      },
      defaultValue: ticket,
      ifShow(renderCallbackParams) {
        return renderCallbackParams.values.is_ticket == 1 ? true : false
      },
      componentProps: {
        options: [
          {
            label: '增值税专用发票',
            value: '增值税专用发票'
          },
          {
            label: '增值税普通发票',
            value: '增值税普通发票'
          }
        ]
      }
    },
    {
      field: 'tax_point',
      label: '开票收的税点',
      component: 'InputNumber',
      required(renderCallbackParams) {
        return renderCallbackParams.values.is_ticket == 1 ? true : false
      },
      defaultValue: ticket,
      ifShow(renderCallbackParams) {
        return renderCallbackParams.values.is_ticket == 1 ? true : false
      },
      componentProps: {
        precision: 2,
        placeholder: '请输入',
        min: 0,
        max: 100,
        // formatter: (value) => `${value}%`,
        addonAfter: '%',
        parser: (value) => value.replace('%', '')
      }
    },
    {
      field: 'Invoicing_is_self',
      label: '开票主体',
      component: 'Select',
      required(renderCallbackParams) {
        return renderCallbackParams.values.is_ticket == 1 ? true : false
      },
      defaultValue: ticket,
      ifShow(renderCallbackParams) {
        return renderCallbackParams.values.is_ticket == 1 ? true : false
      },
      componentProps: {
        options: [
          {
            label: '他人',
            value: 0
          },
          {
            label: '本人',
            value: 1
          }
        ]
      }
    },
    {
      field: 'payment_type',
      label: '付款类型',
      component: 'Select',
      required: true,
      defaultValue: 3,
      componentProps: {
        options: [
          {
            label: '定金',
            value: 1
          },
          {
            label: '最后一笔款',
            value: 2
          },
          {
            label: '全款',
            value: 3
          }
        ]
      }
    },
    {
      field: 'contracting_party',
      label: '我司签约主体',
      component: 'PagingApiSelect',
      required: true,
      componentProps: {
        api: cpgetList,
        selectProps: {
          fieldNames: { key: 'key', value: 'name', label: 'name' },
          showSearch: true,
          placeholder: '请选择',
          allowClear: true,
          optionFilterProp: 'name'
        },
        resultField: 'items'
      },
      dynamicDisabled: true
    },
    {
      field: 'remark',
      label: '付款备注',
      component: 'InputTextArea'
    },
    {
      field: 'files',
      label: '附件',
      component: 'Upload',
      slot: 'Files'
    },
    {
      field: 'account',
      label: '收款账号',
      component: 'Select',
      required: true,

      // itemHelpMessage: '如若不选择收款账号, 则默认为供应商收款账号',
      slot: 'Account'
    },
    {
      field: 'account_name',
      label: '收款账号名',
      component: 'Input',
      dynamicDisabled: true
      // show: false
    },
    {
      field: 'bank',
      label: '开户行',
      component: 'Input',
      dynamicDisabled: true
      // show: false
    },
    {
      field: 'is_public_account',
      label: '是否公户',
      component: 'Select',
      show: false
    }
  ]
}

export const suppliercolumns: BasicColumn[] = [
  // {
  //   title: '收款人名称',
  //   dataIndex: 'name',
  //   width: 100,
  //   resizable: true,
  //   editRow: true,
  //   editComponent: 'Input'
  // },
  {
    title: '收款账号',
    dataIndex: 'account',
    width: 100,
    resizable: true,
    editRow: true,
    editComponent: 'Input'
  },
  {
    title: '收款账号名称',
    dataIndex: 'account_name',
    width: 100,
    resizable: true,
    editRow: true,
    editComponent: 'Input'
  },
  {
    title: '开户行',
    dataIndex: 'bank',
    width: 100,
    resizable: true,
    editRow: true,
    editComponent: 'Input'
  },
  {
    title: '是否公户',
    dataIndex: 'is_public_account',
    width: 100,
    resizable: true,
    editRow: true,
    editComponent: 'Select',
    editComponentProps: {
      options: [
        {
          label: '是',
          value: 1
        },
        {
          label: '否',
          value: 0
        }
      ]
    }
  }
]
function handleallIds(node: any): any {
  const ids: string[] = []

  // 添加当前节点的 id（如果存在）
  if (node.id) {
    ids.push(node.id)
  }

  // 如果有子节点，递归处理并合并结果
  if (node.children && node.children.length > 0) {
    node.children.forEach((item: any) => {
      const childIds = handleallIds(item)
      ids.push(...childIds) // 合并子节点的 id 到主数组
    })
  }

  return ids
}
