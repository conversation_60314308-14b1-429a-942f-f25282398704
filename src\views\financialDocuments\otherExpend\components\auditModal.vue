<template>
  <BasicModal @register="register" width="800px" @ok="handleok">
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>
<script setup lang="ts">
import { BasicModal, useModalInner } from '/@/components/Modal'
import { BasicForm, useForm } from '/@/components/Form'
import { stockodsetManagerStatus } from '/@/api/financialDocuments/otherExpend'

const emit = defineEmits(['relaod'])

const [register, { changeOkLoading, closeModal }] = useModalInner(async (data) => {
  console.log(data)
  resetFields()
  setFieldsValue(data)
})

const [registerForm, { setFieldsValue, validate, resetFields }] = useForm({
  showActionButtonGroup: false,
  layout: 'vertical',
  labelWidth: 200,
  schemas: [
    {
      field: 'manager_status',
      label: 'manager_status',
      component: 'Input',
      show: false
    },
    {
      field: 'id',
      label: 'id',
      component: 'Input',
      show: false
    },
    {
      label: '是否通过',
      field: 'isPass',
      component: 'RadioButtonGroup',
      componentProps: {
        options: [
          {
            label: '通过',
            value: 1
          },
          {
            label: '不通过',
            value: 2
          }
        ]
      },
      colProps: { span: 24 },
      required: true,
      defaultValue: 1
    },
    {
      field: 'manager_status_remark',
      label: '驳回备注',
      component: 'InputTextArea',
      componentProps: {
        autosize: { minRows: 10, maxRows: 6 }
      },
      colProps: { span: 24 },
      show({ model }) {
        return model.isPass === 2
      },
      required({ model }) {
        return model.isPass === 2
      }
    }
  ]
})

async function handleok() {
  try {
    changeOkLoading(true)
    const formdata = await validate()
    const params = {
      id: formdata.id,
      manager_status: formdata.isPass == 1 ? (formdata.manager_status == 0 ? 1 : 2) : formdata.manager_status == 0 ? 3 : 4,
      manager_status_remark: formdata.manager_status_remark || undefined
    }
    stockodsetManagerStatus(params)
    closeModal()
    await changeOkLoading(false)
    emit('relaod')
  } catch (err) {
    console.log(err)
    changeOkLoading(false)
  }
}
</script>
