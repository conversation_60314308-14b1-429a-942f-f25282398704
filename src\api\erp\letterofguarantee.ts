import { defHttp } from '/@/utils/http/axios'

enum Api {
  projectbkgetList = '/erp/project/bk/getList',
  projectbkcreateOrUpdate = '/erp/project/bk/createOrUpdate',
  projectbksetCheck = '/erp/project/bk/setCheck',
  projectbkgetRemark = '/erp/project/bk/getRemark'
}

export const projectbkgetList = (params?: any) => defHttp.get({ url: Api.projectbkgetList, params })
export const projectbkcreateOrUpdate = (params?: any) =>
  defHttp.post({ url: Api.projectbkcreateOrUpdate, params }, { successMessageMode: 'message', errorMessageMode: 'message' })
export const projectbksetCheck = (params?: any) =>
  defHttp.post({ url: Api.projectbksetCheck, params }, { successMessageMode: 'message', errorMessageMode: 'message' })
export const projectbkgetRemark = (params?: any) => defHttp.get({ url: Api.projectbkgetRemark, params })
