import { defHttp } from '/@/utils/http/axios'
import { BasicFetchResult } from '/@/api/model/baseModel'
import { otherExpend } from './modle/types'

enum Api {
  GetOtherExpendList = '/erp/stock/od/getList',
  PostaddExpendList = '/erp/stock/od/add',
  PostupdateExpendList = '/erp/stock/od/update',
  deleteOtherIncome = '/erp/stock/od/remove',
  detailsList = '/erp/stock/od/details',
  getRepList = '/erp/stock/od/getSalesOrder',
  getsetStatus = '/erp/stock/od/setStatus',
  getsetSetCheck = '/erp/stock/od/setCheck',
  getsetReject = '/erp/stock/od/setReject',
  //旧erp附件上传
  getupdateFiles = '/erp/stock/od/updateFiles',
  //确认
  setApprove = '/erp/stock/od/setApprove',
  //支出单明细列表导出
  postexport = '/erp/stock/ods/export',
  //工单
  toolsgetPaOrderInfo = '/tools/getPaOrderInfo',
  //总经理审核
  stockodsetManagerStatus = '/erp/stock/od/setManagerStatus',
  mfgetCashierAndOrder = '/meituan/mf/getCashierAndOrder'
}

export const getOtherExpendList = (params?: {}) => defHttp.get<BasicFetchResult<otherExpend>>({ url: Api.GetOtherExpendList, params })
export const postaddExpendList = (params?: {}) =>
  defHttp.post<BasicFetchResult<otherExpend>>({ url: Api.PostaddExpendList, params }, { isTransformResponse: false })
export const postupdateExpendList = (params?: {}) =>
  defHttp.post<BasicFetchResult<otherExpend>>(
    { url: Api.PostupdateExpendList, params },
    { successMessageMode: 'message', errorMessageMode: 'message' }
  )
export const deleteOtherIncome = (params?: {}) => defHttp.get<BasicFetchResult<otherExpend>>({ url: Api.deleteOtherIncome, params })
export const detailsList = (params?: {}) => defHttp.get<BasicFetchResult<otherExpend>>({ url: Api.detailsList, params })
export const getRepList = (params?: {}) => defHttp.get<BasicFetchResult<otherExpend>>({ url: Api.getRepList, params })
export const getsetStatus = (params?: {}) => defHttp.get<BasicFetchResult<otherExpend>>({ url: Api.getsetStatus, params })
export const getsetSetCheck = (params?: {}) => defHttp.post<BasicFetchResult<otherExpend>>({ url: Api.getsetSetCheck, params })
export const getsetReject = (params?: {}) => defHttp.get<BasicFetchResult<otherExpend>>({ url: Api.getsetReject, params })
export const getupdateFiles = (params?: {}) =>
  defHttp.post<BasicFetchResult<otherExpend>>(
    { url: Api.getupdateFiles, params },
    { successMessageMode: 'message', errorMessageMode: 'message' }
  )
export const setApprove = (params?: {}) => defHttp.get<BasicFetchResult<otherExpend>>({ url: Api.setApprove, params })
export const postexport = (istree, params) =>
  defHttp.get({ url: Api.postexport, params, responseType: istree ? 'json' : 'blob' }, { isTransformResponse: istree })
export const toolsgetPaOrderInfo = (params?: {}) => defHttp.get({ url: Api.toolsgetPaOrderInfo, params })
export const stockodsetManagerStatus = (params?: {}) =>
  defHttp.get({ url: Api.stockodsetManagerStatus, params }, { successMessageMode: 'message', errorMessageMode: 'message' })
export const mfgetCashierAndOrder = (params?: {}) => defHttp.get({ url: Api.mfgetCashierAndOrder, params })
