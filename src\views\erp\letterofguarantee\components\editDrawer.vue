<template>
  <BasicDrawer @register="registerDrawer" @ok="handleSubmit">
    <BasicForm @register="registerForm">
      <template #bkSourceUniqids="{ model }">
        <Tag v-for="item in model.bk_source_uniqids" :key="item" color="#108ee9">{{ item }}</Tag>
      </template>
      <template #bkPackingStrids="{ model }">
        <Tag v-for="item in model.bk_packing_strids" :key="item" color="#108ee9">{{ item }}</Tag>
      </template>
      <template #File>
        <Upload
          v-model:file-list="filesLists.file.value"
          action="/api/oss/putImg2Stocking"
          :custom-request="handleFileRequest1"
          :multiple="true"
          list-type="picture-card"
          :disabled="disabledbloon"
        >
          <div>
            <plus-outlined />
            <div style="margin-top: 8px">Upload</div>
          </div>
        </Upload>
      </template>
      <template #otherfile>
        <Upload
          v-model:file-list="filesLists.other_file.value"
          action="/api/oss/putImg2Stocking"
          list-type="picture-card"
          :custom-request="handleFileRequest2"
          :multiple="true"
          :disabled="disabledbloon"
        >
          <div>
            <plus-outlined />
            <div style="margin-top: 8px">Upload</div>
          </div>
        </Upload>
      </template>
    </BasicForm>
    <BasicTable @register="registerTable" v-if="types == 'detail'" />
  </BasicDrawer>
</template>
<script setup lang="ts">
import { h, nextTick, ref } from 'vue'
import { schemas } from '../datas/editdrawer'
import { BasicDrawer, useDrawerInner } from '/@/components/Drawer'
import { BasicForm, useForm } from '/@/components/Form'
import { UploadRequestOption } from 'ant-design-vue/es/vc-upload/interface'
import { commonFileUpload } from '/@/api/commonUtils/upload'
import { UploadFile, Upload, message, Tag } from 'ant-design-vue'
import { PlusOutlined } from '@ant-design/icons-vue'
import { projectbkcreateOrUpdate, projectbkgetRemark } from '/@/api/erp/letterofguarantee'
import { BasicTable, useTable } from '/@/components/Table'
import { isNullOrUnDef } from '/@/utils/is'

const emit = defineEmits(['success'])
const [registerTable, { setTableData, setLoading }] = useTable({
  title: '审核流程',
  columns: [
    {
      dataIndex: 'creator_name',
      title: '审核人',
      width: 100,
      resizable: true
    },
    {
      dataIndex: 'type',
      title: '类型',
      width: 100,
      resizable: true,
      customRender({ text }) {
        const map = {
          1: { lable: '财务审核', color: 'green' },
          2: { lable: '项目经理审核', color: 'skyblue' },
          3: { lable: '项目经理负责人审核', color: 'blue' }
        }
        return isNullOrUnDef(text)
          ? '-'
          : h(
              Tag,
              {
                color: map[text].color
              },
              map[text].lable
            )
      }
    },
    {
      dataIndex: 'remark',
      title: '审核备注',
      width: 100,
      resizable: true
    }
  ],
  showIndexColumn: false
})

// 统一管理文件列表
const filesLists = {
  file: ref<UploadFile[]>([]),
  other_file: ref<UploadFile[]>([])
}

const disabledbloon = ref(false)
const types = ref('')
const records = ref('')

const [registerDrawer, { changeOkLoading, closeDrawer }] = useDrawerInner(async (data) => {
  const { type, record = {} } = data
  disabledbloon.value = !['add', 'edit'].includes(type)
  types.value = type
  records.value = record

  resetSchema(schemas)
  resetFields()

  // 重置所有文件列表
  Object.values(filesLists).forEach((list) => (list.value = []))

  if (type !== 'add') {
    setFieldsValue(record)
    // 设置文件列表
    Object.entries(filesLists).forEach(([key, list]) => {
      list.value =
        record[key]?.map((file: string) => ({
          name: file,
          url: file,
          uid: Math.random() * 100000
        })) ?? []
    })
  } else {
    setFieldsValue({ id: undefined })
  }
  if (type == 'detail') {
    await nextTick()
    setLoading(true)
    const { items } = await projectbkgetRemark({ doc_id: record.id })
    setTableData(items)
    setLoading(false)
  }
})

const [registerForm, { setFieldsValue, resetSchema, resetFields, validate }] = useForm({
  labelWidth: 120,
  showActionButtonGroup: false,
  actionColOptions: { span: 24 },
  baseColProps: { span: 24 }
})

// 创建处理函数
const createFileHandler = (fileKey: keyof typeof filesLists) => {
  return (options: UploadRequestOption) => handleFileRequest(fileKey, options)
}

const handleFileRequest1 = createFileHandler('file')
const handleFileRequest2 = createFileHandler('other_file')

// 统一的文件处理函数
const handleFileRequest = async (fileKey: keyof typeof filesLists, options: UploadRequestOption) => {
  const { file, onSuccess } = options
  const fileList = filesLists[fileKey]

  try {
    changeOkLoading(true)
    const result = await commonFileUpload(file, 'purchase')

    if (!result?.path) {
      message.error('上传失败')
      fileList.value = fileList.value.filter((item) => item.url)
      return
    }

    onSuccess?.(result.path)

    fileList.value = fileList.value.map((item) => ({
      url: item.url || item.response,
      uid: item.uid,
      name: item.name
    }))

    await setFieldsValue({
      [fileKey]: fileList.value.map((item) => item.url)
    })

    if (fileList.value.every((item) => item.url)) {
      changeOkLoading(false)
    }
  } catch (error: any) {
    console.error('File upload error:', error)
    changeOkLoading(false)

    if (error?.code === 'ERR_NETWORK') {
      fileList.value = fileList.value.filter((item) => item.status === 'done' || item.url)
    } else {
      fileList.value = fileList.value.filter((item) => item.status !== 'error' || item.url)
    }

    throw error
  }
}

async function handleSubmit() {
  try {
    const formdata = await validate()
    console.log(formdata)
    const params = {
      ...formdata,
      pm_name: undefined
    }
    await projectbkcreateOrUpdate(params)
    emit('success')
    closeDrawer()
  } catch (error: any) {
    console.error('File upload error:', error)
    changeOkLoading(false)
    throw error
  }
}
</script>
