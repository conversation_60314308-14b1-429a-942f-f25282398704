import { defHttp } from '/@/utils/http/axios'
import { BasicFetchResult } from '/@/api/model/baseModel'
import { receiptOrder } from './modle/types'

enum Api {
  SetFollowRemark = '/erp/finance/rc/setFollowRemark',
  GetReceiptOrderList = '/erp/finance/rc/getList',
  // AddReceiptOrder = '/erp/finance/rc/add',
  UpdateReceiptOrder = '/erp/finance/rc/update',
  DeleteReceiptOrder = '/erp/finance/rc/remove',
  GetReceiptOrderDetails = '/erp/finance/rc/details',
  AddBatch = '/erp/finance/rc/addBatch',
  //收款款项更改
  setPaymentType = '/erp/finance/rc/setPaymentType',
  //销售单状态
  setStatus = '/erp/finance/rc/setStatus',
  //收款单紧急状态
  setUrgentLevel = '/erp/finance/rc/setUrgentLevel',
  GetReceiptList = '/tools/getReceiptList',
  //驳回
  GetsetReject = '/erp/finance/rc/setReject',
  //明细款项类型更改
  setMxPaymentType = '/erp/finance/rc/setMxPaymentType',
  // 批量关联
  BindFunds = '/erp/finance/rc/bindRunWater',
  //部门修改
  setDeptId = '/erp/finance/rc/setDeptId',
  setDockingRemark = '/erp/finance/rc/setDockingRemark',
  financercsetSinoChinaStatus = '/erp/finance/rc/setSinoChinaStatus'
}

// 设置跟进备注
export const setFollowRemark = (data: { id: number; follow_remark: string }) => defHttp.post({ url: Api.SetFollowRemark, data })

// 获取收款单(有数据权限的)
export const getReceiptOrderList = (params?: {}) => defHttp.get<BasicFetchResult<receiptOrder>>({ url: Api.GetReceiptOrderList, params })

//获取收款单(后端没有数据权限的)
export const getReceiptList = (params?: Recordable) => defHttp.get<BasicFetchResult<receiptOrder>>({ url: Api.GetReceiptList, params })

// 新增收款单
// export const addReceiptOrder = (params?: { amount: number; processor: string; works: Array<Recordable> }) =>
//   defHttp.post<BasicFetchResult<any>>({ url: Api.AddReceiptOrder, params })

// 关联流水
export const updateReceiptOrder = (params?: {
  fdoc_id: number // 收款单id
  amount: string // 本次应付总金额
  processor: number // 关联人id
  works: Array<Recordable>
  funds: Array<Recordable>
  collection_at: string // 收款日期
  g_remark: string //携带备注修改
  fund_at: string //水单日期
  contracting_party: string // 合同甲方
}) => defHttp.post<BasicFetchResult<any>>({ url: Api.UpdateReceiptOrder, params })

// 获取收款单详情
export const getReceiptOrderDetails = (params: { id: number }) => defHttp.get({ url: Api.GetReceiptOrderDetails, params })

// 删除收款单
export const deleteReceiptOrder = (params?: {}) => defHttp.get<BasicFetchResult<any>>({ url: Api.DeleteReceiptOrder, params })

// 生成收款单
export const addBatch = (params: {
  works: Array<{ work_id: number; amount: string }>
  client_id: number
  collection_at: string
  notes: string
}) =>
  defHttp.post(
    { url: Api.AddBatch, params },
    // { successMessageMode: 'message', errorMessageMode: 'message' },
    { isTransformResponse: false }
  )
// 生成收款单
export const receiptsetPaymentType = (params: {}) => defHttp.get({ url: Api.setPaymentType, params })
// 销售单状态
export const setStatus = (params: {}) => defHttp.get({ url: Api.setStatus, params })
//收款单紧急状态
export const receiptsetUrgentLevel = (params: {}) => defHttp.get({ url: Api.setUrgentLevel, params })
//收款单紧急状态
export const getsetReject = (params: {}) => defHttp.get({ url: Api.GetsetReject, params })
//明细款项类型更改
export const setMxPaymentType = (params: {}) => defHttp.get({ url: Api.setMxPaymentType, params })

// 批量关联流水
export const bindFunds = (data: {}) => defHttp.post({ url: Api.BindFunds, data })
// 批量关联流水
export const setDeptId = (params) => defHttp.get({ url: Api.setDeptId, params })
export const setDockingRemark = (params) =>
  defHttp.post({ url: Api.setDockingRemark, params }, { successMessageMode: 'message', errorMessageMode: 'message' })
export const GetReceiptOrderListExcel = (params) =>
  defHttp.get(
    { url: Api.GetReceiptOrderList, params, responseType: 'blob' },
    { successMessageMode: 'message', errorMessageMode: 'message', isTransformResponse: false }
  )

export const financercsetSinoChinaStatus = (params?: {}) =>
  defHttp.get({ url: Api.financercsetSinoChinaStatus, params }, { successMessageMode: 'message', errorMessageMode: 'message' })
