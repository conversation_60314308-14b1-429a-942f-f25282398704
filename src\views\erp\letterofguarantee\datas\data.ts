import { h } from 'vue'
import { BasicColumn, FormSchema } from '/@/components/Table'
import { isNullOrUnDef } from '/@/utils/is'
import { Tag } from 'ant-design-vue'
import { getProjectList } from '/@/api/revisit'

const commonMap = {
  0: { label: '未审核', color: '' },
  1: { label: '不通过', color: 'red' },
  2: { label: '通过', color: 'green' }
}

export const columns: BasicColumn[] = [
  {
    dataIndex: 'project_number',
    title: '项目ID',
    width: 100,
    resizable: true
  },
  {
    dataIndex: 'pm_name',
    title: '项目经理',
    width: 100,
    resizable: true
  },
  {
    dataIndex: 'leader_name',
    title: '项目经理负责人名称',
    width: 100,
    resizable: true
  },
  {
    dataIndex: 'file',
    title: '需盖章文件',
    width: 100,
    resizable: true
  },
  {
    dataIndex: 'other_file',
    title: '其他附件',
    width: 100,
    resizable: true
  },
  {
    dataIndex: 'is_receip',
    title: '是否收齐款',
    width: 100,
    resizable: true,
    customRender: ({ text }) => {
      return isNullOrUnDef(text) ? '' : h(Tag, { color: text == 2 ? 'green' : 'red' }, () => (text == 2 ? '是' : '否'))
    }
  },
  {
    dataIndex: 'status',
    title: '状态',
    width: 100,
    resizable: true,
    customRender: ({ value }) => {
      return isNullOrUnDef(value) ? '' : h(Tag, { color: commonMap[value].color }, () => commonMap[value].label)
    }
  },
  {
    dataIndex: 'is_finance',
    title: '财务审核',
    width: 100,
    resizable: true,
    customRender: ({ value }) => {
      console.log(value)
      return isNullOrUnDef(value) ? '' : h(Tag, { color: commonMap[value].color }, () => commonMap[value].label)
    }
  },
  {
    dataIndex: 'is_pm',
    title: '项目经理审核',
    width: 100,
    resizable: true,
    customRender: ({ value }) => {
      return isNullOrUnDef(value) ? '' : h(Tag, { color: commonMap[value].color }, () => commonMap[value].label)
    }
  },
  {
    dataIndex: 'is_pm_charger',
    title: '项目经理负责人审核',
    width: 100,
    resizable: true,
    customRender: ({ value }) => {
      return isNullOrUnDef(value) ? '' : h(Tag, { color: commonMap[value].color }, () => commonMap[value].label)
    }
  }
]

export const columnsschemas: FormSchema[] = [
  {
    field: 'project_number',
    label: '项目ID',
    component: 'PagingApiSelect',
    componentProps: {
      api: getProjectList,
      resultField: 'items',
      labelField: 'title',
      valueField: 'id',
      searchMode: true,
      pagingMode: true,
      searchParamField: 'project_number',
      selectProps: {
        fieldNames: {
          key: 'key',
          value: 'id',
          label: 'project_number'
        },
        showSearch: true,
        placeholder: '请选择',
        optionFilterProp: 'id',
        allowClear: true
      }
    },
    itemProps: {
      validateTrigger: 'blur'
    },
    colProps: {
      span: 6
    }
  },
  {
    field: 'is_receip',
    label: '是否收齐款',
    component: 'Select',
    componentProps: {
      options: [
        {
          label: '是',
          value: 2
        },
        {
          label: '否',
          value: 1
        }
      ]
    },
    colProps: {
      span: 6
    }
  },
  {
    field: 'status',
    label: '状态',
    component: 'Select',
    componentProps: {
      options: [
        {
          label: '未审核',
          value: 0
        },
        {
          label: '不通过',
          value: 1
        },
        {
          label: '通过',
          value: 2
        }
      ]
    },
    colProps: {
      span: 6
    }
  },
  {
    field: 'is_finance',
    label: '财务审核',
    component: 'Select',
    componentProps: {
      options: [
        {
          label: '未审核',
          value: 0
        },
        {
          label: '不通过',
          value: 1
        },
        {
          label: '通过',
          value: 2
        }
      ]
    },
    colProps: {
      span: 6
    }
  },
  {
    field: 'is_pm',
    label: '项目经理审核',
    component: 'Select',
    componentProps: {
      options: [
        {
          label: '未审核',
          value: 0
        },
        {
          label: '不通过',
          value: 1
        },
        {
          label: '通过',
          value: 2
        }
      ]
    },
    colProps: {
      span: 6
    }
  },
  {
    field: 'is_pm_charger',
    label: '项目经理负责人审核',
    component: 'Select',
    componentProps: {
      options: [
        {
          label: '未审核',
          value: 0
        },
        {
          label: '不通过',
          value: 1
        },
        {
          label: '通过',
          value: 2
        }
      ]
    },
    colProps: {
      span: 6
    }
  }
]
